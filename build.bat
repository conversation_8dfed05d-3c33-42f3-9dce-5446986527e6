@echo off
chcp 65001 >nul
echo ====================================
echo Free AugmentCode 一键打包工具
echo ====================================
echo.

echo 正在检查 Python 环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到 Python，请先安装 Python 3.10 或更高版本
    pause
    exit /b 1
)

echo ✅ Python 环境检查通过
echo.

echo 正在安装依赖包...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ 错误: 依赖包安装失败
    pause
    exit /b 1
)

echo ✅ 依赖包安装完成
echo.

echo 开始打包程序...
python build.py
if errorlevel 1 (
    echo ❌ 错误: 打包失败
    pause
    exit /b 1
)

echo.
echo 🎉 打包完成！
echo 📁 可执行文件位于 dist 目录中
echo.
pause
