@echo off
chcp 65001 >nul
title Free AugmentCode - VSCode数据清理工具

echo ====================================
echo Free AugmentCode - VSCode数据清理工具
echo ====================================
echo.

REM 检查exe文件是否存在
if not exist "dist\FreeAugmentCode.exe" (
    echo ❌ 错误: 找不到 dist\FreeAugmentCode.exe
    echo 请先运行 build.bat 进行打包
    echo.
    pause
    exit /b 1
)

echo 正在启动程序...
echo.

REM 运行exe程序
"dist\FreeAugmentCode.exe"

REM 无论程序如何退出，都暂停等待用户
echo.
echo ====================================
echo 程序已结束
echo ====================================
pause
