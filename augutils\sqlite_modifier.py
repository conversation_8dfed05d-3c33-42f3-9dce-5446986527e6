import sqlite3
import shutil
import time
import os
from utils.paths import get_db_path

def _create_backup(file_path: str) -> str:
    """
    创建指定文件的带时间戳备份

    Args:
        file_path (str): 要备份的文件路径

    Returns:
        str: 备份文件路径

    格式: <文件名>.bak.<时间戳>
    """
    timestamp = int(time.time())
    backup_path = f"{file_path}.bak.{timestamp}"
    shutil.copy2(file_path, backup_path)
    return backup_path

def check_augment_data_exists() -> dict:
    """
    检查数据库中是否存在augment相关数据

    Returns:
        dict: 检查结果
        {
            'exists': bool,         # 是否存在相关数据
            'count': int,          # 记录数量
            'sample_keys': list,   # 示例键名
            'error': str           # 错误信息（如果有）
        }
    """
    db_path = get_db_path()

    # 检查数据库文件是否存在
    if not os.path.exists(db_path):
        return {
            'exists': False,
            'count': 0,
            'sample_keys': [],
            'error': f"数据库文件不存在: {db_path}"
        }

    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='ItemTable'")
        if not cursor.fetchone():
            return {
                'exists': False,
                'count': 0,
                'sample_keys': [],
                'error': "数据库中没有找到 ItemTable 表"
            }

        # 查询augment相关记录数量
        cursor.execute("SELECT COUNT(*) FROM ItemTable WHERE key LIKE '%augment%'")
        count = cursor.fetchone()[0]

        # 获取示例键名
        sample_keys = []
        if count > 0:
            cursor.execute("SELECT key FROM ItemTable WHERE key LIKE '%augment%' LIMIT 5")
            sample_keys = [row[0] for row in cursor.fetchall()]

        return {
            'exists': count > 0,
            'count': count,
            'sample_keys': sample_keys,
            'error': None
        }

    except Exception as e:
        return {
            'exists': False,
            'count': 0,
            'sample_keys': [],
            'error': f"数据库检查失败: {e}"
        }
    finally:
        try:
            cursor.close()
            conn.close()
        except:
            pass

def clean_augment_data() -> dict:
    """
    清理SQLite数据库中的augment相关数据
    修改前会创建备份

    此函数执行以下操作:
    1. 获取SQLite数据库路径
    2. 创建数据库文件备份
    3. 打开数据库连接
    4. 删除包含'augment'的记录

    Returns:
        dict: 包含操作结果的字典
        {
            'db_backup_path': str,  # 数据库备份路径
            'deleted_rows': int     # 删除的行数
        }
    """
    db_path = get_db_path()

    # 检查数据库文件是否存在
    import os
    if not os.path.exists(db_path):
        raise FileNotFoundError(f"数据库文件不存在: {db_path}")

    # 修改前创建备份
    db_backup_path = _create_backup(db_path)

    # 连接数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    try:
        # 首先检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='ItemTable'")
        if not cursor.fetchone():
            print("⚠️  警告: 数据库中没有找到 ItemTable 表")
            return {
                'db_backup_path': db_backup_path,
                'deleted_rows': 0
            }

        # 查看删除前有多少条匹配记录
        cursor.execute("SELECT COUNT(*) FROM ItemTable WHERE key LIKE '%augment%'")
        count_before = cursor.fetchone()[0]
        print(f"找到 {count_before} 条包含 'augment' 的记录")

        # 如果没有找到相关数据，抛出异常
        if count_before == 0:
            raise ValueError("数据库中没有找到包含 'augment' 的相关数据，可能VSCode从未安装AugmentCode插件或数据已被清理")

        # 显示将要删除的具体记录
        cursor.execute("SELECT key FROM ItemTable WHERE key LIKE '%augment%' LIMIT 10")
        sample_keys = cursor.fetchall()
        print("将要删除的记录示例:")
        for i, (key,) in enumerate(sample_keys, 1):
            print(f"  {i}. {key}")
        if count_before > 10:
            print(f"  ... 还有 {count_before - 10} 条记录")

        # 执行删除查询
        cursor.execute("DELETE FROM ItemTable WHERE key LIKE '%augment%'")
        deleted_rows = cursor.rowcount

        # 提交更改
        conn.commit()

        print(f"✅ 成功删除 {deleted_rows} 条记录")

        return {
            'db_backup_path': db_backup_path,
            'deleted_rows': deleted_rows,
            'found_records': count_before
        }
    except Exception as e:
        # 回滚事务
        conn.rollback()
        print(f"数据库操作失败: {e}")
        raise
    finally:
        # 总是关闭连接
        cursor.close()
        conn.close()