#!/usr/bin/env python3
"""
Free AugmentCode 打包脚本
使用 PyInstaller 将项目打包成独立的可执行文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def check_dependencies():
    """检查必要的依赖是否已安装"""
    try:
        import psutil
        import PyInstaller
        print("✅ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return False


def clean_build_dirs():
    """清理之前的构建目录"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"清理目录: {dir_name}")
            shutil.rmtree(dir_name)
    
    # 清理 .spec 文件
    spec_files = list(Path('.').glob('*.spec'))
    for spec_file in spec_files:
        print(f"删除文件: {spec_file}")
        spec_file.unlink()


def create_spec_file():
    """创建 PyInstaller 配置文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['index.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'psutil',
        'sqlite3',
        'json',
        'uuid',
        'secrets',
        'pathlib',
        'shutil',
        'time',
        'os',
        'sys',
        # 项目模块
        'utils',
        'utils.paths',
        'utils.device_codes',
        'augutils',
        'augutils.json_modifier',
        'augutils.sqlite_modifier',
        'augutils.workspace_cleaner',
        'augutils.process_manager'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='FreeAugmentCode',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
    
    with open('FreeAugmentCode.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 已创建 PyInstaller 配置文件: FreeAugmentCode.spec")


def build_executable():
    """构建可执行文件"""
    print("开始构建可执行文件...")
    
    try:
        # 使用 PyInstaller 构建
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--clean',
            '--noconfirm',
            'FreeAugmentCode.spec'
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        
        print("✅ 构建成功!")
        print(f"可执行文件位置: {os.path.abspath('dist/FreeAugmentCode.exe')}")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False


def create_readme():
    """创建使用说明文件"""
    readme_content = """# Free AugmentCode 可执行文件

## 使用说明

1. **关闭 VS Code**: 运行程序前请确保完全关闭 VS Code
2. **运行程序**: 双击 `FreeAugmentCode.exe` 或在命令行中运行
3. **按提示操作**: 程序会自动检测并关闭 VS Code 进程，然后清理相关数据
4. **重启 VS Code**: 程序执行完成后，重新启动 VS Code 并使用新邮箱登录

## 功能说明

- 🔄 自动检测并关闭 VS Code 进程
- 📝 修改 Telemetry ID (设备 ID 和机器 ID)
- 🗃️ 清理 SQLite 数据库中的相关记录
- 💾 清理工作区存储文件
- 📦 自动备份所有修改的文件

## 注意事项

- 程序会自动创建备份文件，以防需要恢复
- 如果 VS Code 进程无法正常关闭，程序会询问是否强制关闭
- 建议在运行前保存所有重要工作

## 故障排除

如果遇到问题，请检查：
1. 是否有足够的权限访问 VS Code 配置文件
2. 是否有其他程序正在使用 VS Code 相关文件
3. 杀毒软件是否阻止了程序运行

## 版本信息

构建时间: {build_time}
Python 版本: {python_version}
"""
    
    import datetime
    
    readme_content = readme_content.format(
        build_time=datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        python_version=sys.version
    )
    
    with open('dist/README.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ 已创建使用说明文件: dist/README.txt")


def main():
    """主函数"""
    print("=" * 60)
    print("Free AugmentCode 打包工具")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        return 1
    
    # 清理构建目录
    clean_build_dirs()
    
    # 创建配置文件
    create_spec_file()
    
    # 构建可执行文件
    if not build_executable():
        return 1
    
    # 创建说明文件
    if os.path.exists('dist'):
        create_readme()
    
    print("\n" + "=" * 60)
    print("🎉 打包完成!")
    print(f"📁 输出目录: {os.path.abspath('dist')}")
    print("📄 可执行文件: FreeAugmentCode.exe")
    print("📖 使用说明: README.txt")
    print("=" * 60)
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
