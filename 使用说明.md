# Free AugmentCode 使用说明

## 新增功能

### 1. 自动关闭 VSCode 功能
- ✅ 程序启动时自动检测并关闭所有 VSCode 相关进程
- ✅ 支持优雅关闭和强制关闭两种模式
- ✅ 实时显示进程关闭状态
- ✅ 如果无法关闭进程会提示用户选择是否继续

### 2. 一键打包成 EXE 功能
- ✅ 提供完整的打包脚本和配置
- ✅ 支持 Windows 一键打包批处理文件
- ✅ 自动处理依赖和配置

### 3. 自动重启 VSCode 功能 ⭐ 新增
- ✅ 智能检测系统中的 VSCode 安装路径
- ✅ 清理完成后自动重新启动 VSCode
- ✅ 支持用户选择是否要重启 VSCode
- ✅ 命令行参数控制：`--auto-restart` 和 `--no-restart`
- ✅ 当找不到 VSCode 时提供详细的帮助信息

## 快速开始

### 方式一：直接运行 Python 脚本

1. **安装依赖**：
   ```bash
   pip install -r requirements.txt
   ```

2. **正常运行**：
   ```bash
   python index.py
   ```

3. **调试模式运行**（跳过VSCode关闭检查）：
   ```bash
   python index.py --debug
   # 或者
   python index.py -d
   # 或者双击运行
   debug.bat
   ```

4. **VSCode重启控制**：
   ```bash
   # 自动重启VSCode（无需用户确认）
   python index.py --auto-restart

   # 跳过VSCode重启（不询问用户）
   python index.py --no-restart

   # 组合使用
   python index.py --debug --auto-restart

   # 或使用便捷脚本
   auto-restart.bat    # 自动重启模式
   no-restart.bat      # 无重启模式
   ```

### 方式二：打包成 EXE 文件

1. **一键打包**（推荐）：
   - 双击运行 `build.bat`
   - 等待打包完成

2. **手动打包**：
   ```bash
   pip install -r requirements.txt
   python build.py
   ```

3. **运行 EXE**：
   - 打包完成后，在 `dist` 目录中找到 `FreeAugmentCode.exe`
   - 正常运行：双击 `FreeAugmentCode.exe`
   - 调试模式：在命令行运行 `FreeAugmentCode.exe --debug`

## 程序执行流程

1. **启动检查**：
   - 显示程序标题和版本信息
   - 检测当前运行的 VSCode 进程

2. **关闭 VSCode**：
   - 自动发送终止信号给所有 VSCode 进程
   - 等待进程正常关闭（5秒超时）
   - 如有必要，强制关闭剩余进程

3. **数据清理**：
   - 修改 Telemetry ID（设备 ID 和机器 ID）
   - 清理 SQLite 数据库中的相关记录
   - 清理工作区存储文件

4. **备份管理**：
   - 自动备份所有被修改的文件
   - 显示备份文件位置

5. **VSCode重启**：
   - 询问用户是否要自动重启 VSCode（默认模式）
   - 或根据命令行参数自动处理重启逻辑
   - 智能检测 VSCode 安装路径并启动

6. **完成提示**：
   - 显示操作结果和 VSCode 启动状态
   - 提示用户登录新邮箱

## 文件结构

```
free-augmentcode/
├── index.py                    # 主程序入口
├── requirements.txt            # Python 依赖列表
├── build.py                   # 打包脚本
├── build.bat                  # Windows 一键打包批处理
├── debug.bat                  # 调试模式启动脚本
├── auto-restart.bat           # 自动重启模式脚本（新增）
├── no-restart.bat             # 无重启模式脚本（新增）
├── 使用说明.md                 # 本文件
├── augutils/                  # 核心工具模块
│   ├── __init__.py
│   ├── json_modifier.py       # JSON 文件修改工具
│   ├── sqlite_modifier.py     # SQLite 数据库修改工具
│   ├── workspace_cleaner.py   # 工作区清理工具
│   └── process_manager.py     # 进程管理工具（新增）
└── utils/                     # 通用工具模块
    ├── __init__.py
    ├── paths.py              # 路径管理工具
    └── device_codes.py       # 设备代码生成工具
```

## 新增依赖

- **psutil**: 用于进程管理和系统信息获取
- **pyinstaller**: 用于将 Python 脚本打包成可执行文件

## 安全说明

1. **权限要求**：
   - 程序需要足够权限来终止 VSCode 进程
   - 需要读写 VSCode 配置文件的权限

2. **数据安全**：
   - 所有修改的文件都会自动备份
   - 备份文件包含时间戳，便于识别和恢复

3. **杀毒软件**：
   - 某些杀毒软件可能会误报 EXE 文件
   - 如遇到问题，请将程序添加到白名单

## 故障排除

### 1. VSCode 进程无法关闭
- **原因**：权限不足或进程被其他程序锁定
- **解决**：以管理员身份运行程序，或手动关闭 VSCode

### 2. 打包失败
- **原因**：缺少依赖或 Python 版本不兼容
- **解决**：确保 Python 版本 ≥ 3.10，重新安装依赖

### 3. EXE 文件无法运行
- **原因**：杀毒软件拦截或系统兼容性问题
- **解决**：添加到杀毒软件白名单，或使用 Python 脚本方式运行

### 4. 配置文件未找到
- **原因**：VSCode 未安装或使用了非标准安装路径
- **解决**：确保 VSCode 已正确安装，或检查路径配置

### 5. VSCode 自动重启失败
- **原因**：VSCode 未安装、安装路径非标准、或权限不足
- **解决**：
  - 确保已安装 Visual Studio Code
  - 检查常见安装位置是否存在 Code.exe
  - 使用 `--no-restart` 参数跳过自动重启，手动启动 VSCode
  - 以管理员身份运行程序

## 更新日志

### v2.1.0 (当前版本) ⭐ 最新
- ✅ 新增自动重启 VSCode 功能
- ✅ 智能检测 VSCode 安装路径
- ✅ 支持命令行参数控制重启行为
- ✅ 添加便捷的批处理脚本
- ✅ 改进错误处理和用户提示

### v2.0.0
- ✅ 新增自动关闭 VSCode 功能
- ✅ 新增 EXE 打包功能
- ✅ 改进用户界面和提示信息
- ✅ 增强错误处理和异常捕获
- ✅ 添加详细的使用说明和故障排除指南

### v1.0.0 (原始版本)
- ✅ 基础的 Telemetry ID 修改功能
- ✅ SQLite 数据库清理功能
- ✅ 工作区存储清理功能

## 技术支持

如果您在使用过程中遇到问题，请：

1. 查看本使用说明的故障排除部分
2. 检查是否有足够的系统权限
3. 确认 VSCode 和相关文件路径是否正确
4. 提供详细的错误信息以便诊断

## 免责声明

本工具仅用于学习和研究目的，使用者需自行承担使用风险。建议在使用前备份重要数据。
