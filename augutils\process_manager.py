import os
import sys
import time
import psutil
import subprocess
from typing import List, Dict, Optional


def find_vscode_processes() -> List[Dict[str, any]]:
    """
    查找所有运行中的VSCode相关进程
    
    Returns:
        List[Dict]: 包含进程信息的字典列表
        每个字典包含: {'pid': int, 'name': str, 'cmdline': List[str]}
    """
    vscode_processes = []
    vscode_names = [
        'code.exe', 'code', 'Code.exe', 'Code',
        'code-insiders.exe', 'code-insiders',
        'Code - Insiders.exe', 'Code - Insiders'
    ]
    
    try:
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                proc_info = proc.info
                if proc_info['name'] and any(name.lower() in proc_info['name'].lower() for name in vscode_names):
                    vscode_processes.append({
                        'pid': proc_info['pid'],
                        'name': proc_info['name'],
                        'cmdline': proc_info['cmdline'] or []
                    })
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                # 忽略无法访问的进程
                continue
    except Exception as e:
        print(f"查找VSCode进程时出错: {e}")
    
    return vscode_processes


def close_vscode_processes(force: bool = False, timeout: int = 10) -> Dict[str, any]:
    """
    关闭所有VSCode相关进程
    
    Args:
        force (bool): 是否强制关闭进程
        timeout (int): 等待进程正常关闭的超时时间（秒）
    
    Returns:
        Dict: 操作结果
        {
            'found_processes': int,
            'closed_processes': int,
            'failed_processes': List[Dict],
            'success': bool
        }
    """
    print("正在查找VSCode进程...")
    vscode_processes = find_vscode_processes()
    
    if not vscode_processes:
        print("未发现运行中的VSCode进程")
        return {
            'found_processes': 0,
            'closed_processes': 0,
            'failed_processes': [],
            'success': True
        }
    
    print(f"发现 {len(vscode_processes)} 个VSCode相关进程")
    for proc in vscode_processes:
        print(f"  - PID: {proc['pid']}, 名称: {proc['name']}")
    
    closed_count = 0
    failed_processes = []
    
    # 首先尝试优雅关闭
    if not force:
        print("尝试优雅关闭VSCode进程...")
        for proc_info in vscode_processes:
            try:
                proc = psutil.Process(proc_info['pid'])
                proc.terminate()
                print(f"已发送终止信号给进程 {proc_info['pid']} ({proc_info['name']})")
            except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                print(f"无法终止进程 {proc_info['pid']}: {e}")
                failed_processes.append(proc_info)
        
        # 等待进程关闭
        print(f"等待 {timeout} 秒让进程正常关闭...")
        time.sleep(timeout)
        
        # 检查哪些进程已经关闭
        still_running = []
        for proc_info in vscode_processes:
            try:
                proc = psutil.Process(proc_info['pid'])
                if proc.is_running():
                    still_running.append(proc_info)
                else:
                    closed_count += 1
            except psutil.NoSuchProcess:
                # 进程已经不存在，说明已关闭
                closed_count += 1
        
        vscode_processes = still_running
    
    # 强制关闭剩余进程
    if vscode_processes:
        print(f"强制关闭剩余的 {len(vscode_processes)} 个进程...")
        for proc_info in vscode_processes:
            try:
                proc = psutil.Process(proc_info['pid'])
                proc.kill()
                closed_count += 1
                print(f"已强制关闭进程 {proc_info['pid']} ({proc_info['name']})")
            except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                print(f"无法强制关闭进程 {proc_info['pid']}: {e}")
                failed_processes.append(proc_info)
    
    success = len(failed_processes) == 0
    total_found = len(find_vscode_processes()) if not success else closed_count
    
    if success:
        print("✅ 所有VSCode进程已成功关闭")
    else:
        print(f"⚠️  有 {len(failed_processes)} 个进程无法关闭")
    
    return {
        'found_processes': total_found,
        'closed_processes': closed_count,
        'failed_processes': failed_processes,
        'success': success
    }


def wait_for_vscode_close(max_wait: int = 30) -> bool:
    """
    等待VSCode完全关闭
    
    Args:
        max_wait (int): 最大等待时间（秒）
    
    Returns:
        bool: 是否成功关闭所有VSCode进程
    """
    print(f"等待VSCode完全关闭（最多等待 {max_wait} 秒）...")
    
    for i in range(max_wait):
        vscode_processes = find_vscode_processes()
        if not vscode_processes:
            print("✅ VSCode已完全关闭")
            return True
        
        if i % 5 == 0:  # 每5秒打印一次状态
            print(f"仍有 {len(vscode_processes)} 个VSCode进程运行中...")
        
        time.sleep(1)
    
    print("⚠️  等待超时，VSCode可能未完全关闭")
    return False


def ensure_vscode_closed(force_after_timeout: bool = True) -> bool:
    """
    确保VSCode完全关闭的完整流程
    
    Args:
        force_after_timeout (bool): 超时后是否强制关闭
    
    Returns:
        bool: 是否成功关闭所有VSCode进程
    """
    print("=" * 50)
    print("开始关闭VSCode进程...")
    print("=" * 50)
    
    # 首次尝试优雅关闭
    result = close_vscode_processes(force=False, timeout=5)
    
    if result['success']:
        return True
    
    # 如果有进程无法关闭，等待一段时间后强制关闭
    if force_after_timeout:
        print("部分进程无法优雅关闭，将强制关闭...")
        result = close_vscode_processes(force=True, timeout=0)
        return result['success']
    
    return False


def find_vscode_executable() -> Optional[str]:
    """
    查找VSCode可执行文件路径

    Returns:
        Optional[str]: VSCode可执行文件路径，如果未找到则返回None
    """
    # 常见的VSCode安装路径
    common_paths = []

    if sys.platform == "win32":
        # Windows路径 - 按优先级排序
        username = os.getenv('USERNAME', '')
        common_paths = [
            rf"C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe",
            r"C:\Program Files\Microsoft VS Code\Code.exe",
            r"C:\Program Files (x86)\Microsoft VS Code\Code.exe",
            rf"C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code Insiders\Code - Insiders.exe",
            r"C:\Program Files\Microsoft VS Code Insiders\Code - Insiders.exe",
            r"C:\Program Files (x86)\Microsoft VS Code Insiders\Code - Insiders.exe",
        ]
    elif sys.platform == "darwin":
        # macOS路径
        common_paths = [
            "/Applications/Visual Studio Code.app/Contents/MacOS/Electron",
            "/Applications/Visual Studio Code.app/Contents/Resources/app/bin/code",
            "/Applications/Visual Studio Code - Insiders.app/Contents/MacOS/Electron",
            "/Applications/Visual Studio Code - Insiders.app/Contents/Resources/app/bin/code-insiders",
        ]
    else:
        # Linux路径
        common_paths = [
            "/usr/bin/code",
            "/usr/local/bin/code",
            "/snap/bin/code",
            "/usr/bin/code-insiders",
            "/usr/local/bin/code-insiders",
        ]

    # 检查常见路径
    for path in common_paths:
        if os.path.exists(path):
            # 验证这确实是VSCode而不是其他编辑器
            if 'code' in os.path.basename(path).lower() and 'cursor' not in path.lower():
                return path

    # 尝试在PATH中查找，但要过滤掉非VSCode的程序
    try:
        if sys.platform == "win32":
            result = subprocess.run(['where', 'code'], capture_output=True, text=True, shell=True)
            if result.returncode == 0:
                paths = result.stdout.strip().split('\n')
                for path in paths:
                    # 过滤掉Cursor等其他编辑器，并且确保是.exe文件
                    if ('cursor' not in path.lower() and
                        'microsoft vs code' in path.lower() and
                        path.lower().endswith('.exe')):
                        return path

                # 如果找到了bin/code，尝试找到对应的Code.exe
                for path in paths:
                    if 'cursor' not in path.lower() and 'bin\\code' in path:
                        # 从bin/code路径推导出Code.exe路径
                        code_exe_path = path.replace('bin\\code', 'Code.exe')
                        if os.path.exists(code_exe_path):
                            return code_exe_path

                # 如果没找到Microsoft VS Code，返回第一个不是Cursor的.exe文件
                for path in paths:
                    if 'cursor' not in path.lower() and path.lower().endswith('.exe'):
                        return path
        else:
            result = subprocess.run(['which', 'code'], capture_output=True, text=True)
            if result.returncode == 0:
                path = result.stdout.strip()
                if 'cursor' not in path.lower():
                    return path
    except Exception:
        pass

    return None


def launch_vscode(workspace_path: Optional[str] = None, delay: int = 3, custom_path: Optional[str] = None) -> Dict[str, any]:
    """
    启动VSCode

    Args:
        workspace_path (Optional[str]): 要打开的工作区路径，如果为None则打开空白窗口
        delay (int): 启动前的延迟时间（秒）
        custom_path (Optional[str]): 自定义VSCode可执行文件路径

    Returns:
        Dict: 启动结果
        {
            'success': bool,
            'executable_path': str,
            'process_id': Optional[int],
            'error': Optional[str]
        }
    """
    print(f"准备启动VSCode...")

    # 查找VSCode可执行文件
    vscode_path = custom_path if custom_path else find_vscode_executable()
    if not vscode_path:
        error_msg = """未找到VSCode可执行文件。

可能的原因：
1. VSCode未安装
2. VSCode安装在非标准位置
3. 系统PATH中没有VSCode

建议：
1. 请确保已安装Visual Studio Code
2. 或者手动启动VSCode
3. 常见安装位置：
   - C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe
   - C:\\Program Files\\Microsoft VS Code\\Code.exe"""

        return {
            'success': False,
            'executable_path': None,
            'process_id': None,
            'error': error_msg
        }

    print(f"找到VSCode: {vscode_path}")

    # 等待一段时间确保之前的进程完全关闭
    if delay > 0:
        print(f"等待 {delay} 秒后启动VSCode...")
        time.sleep(delay)

    try:
        # 构建启动命令
        cmd = [vscode_path]
        if workspace_path and os.path.exists(workspace_path):
            cmd.append(workspace_path)
            print(f"将打开工作区: {workspace_path}")
        else:
            print("将打开空白VSCode窗口")

        # 启动VSCode
        if sys.platform == "win32":
            # Windows下使用CREATE_NEW_PROCESS_GROUP避免继承父进程的控制台
            process = subprocess.Popen(
                cmd,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )
        else:
            # Unix系统
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL,
                preexec_fn=os.setsid if hasattr(os, 'setsid') else None
            )

        print(f"✅ VSCode已启动 (PID: {process.pid})")

        return {
            'success': True,
            'executable_path': vscode_path,
            'process_id': process.pid,
            'error': None
        }

    except Exception as e:
        error_msg = f"启动VSCode失败: {e}"
        print(f"❌ {error_msg}")
        return {
            'success': False,
            'executable_path': vscode_path,
            'process_id': None,
            'error': error_msg
        }


def restart_vscode(workspace_path: Optional[str] = None, close_delay: int = 5, start_delay: int = 3) -> Dict[str, any]:
    """
    重启VSCode（关闭后重新启动）

    Args:
        workspace_path (Optional[str]): 要打开的工作区路径
        close_delay (int): 关闭进程的等待时间（秒）
        start_delay (int): 启动前的延迟时间（秒）

    Returns:
        Dict: 重启结果
        {
            'close_success': bool,
            'launch_success': bool,
            'launch_result': Dict
        }
    """
    print("=" * 50)
    print("重启VSCode...")
    print("=" * 50)

    # 关闭VSCode
    close_success = ensure_vscode_closed()

    # 启动VSCode
    launch_result = launch_vscode(workspace_path, start_delay)

    return {
        'close_success': close_success,
        'launch_success': launch_result['success'],
        'launch_result': launch_result
    }


if __name__ == "__main__":
    # 测试功能
    print("Process Manager 测试模块")
    print("可用函数:")
    print("- find_vscode_processes()")
    print("- ensure_vscode_closed()")
    print("- launch_vscode()")
    print("- restart_vscode()")
