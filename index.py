import sys
from utils.paths import get_home_dir, get_app_data_dir, get_storage_path, get_db_path, get_machine_id_path,get_workspace_storage_path
from augutils.json_modifier import modify_telemetry_ids
from augutils.sqlite_modifier import clean_augment_data, check_augment_data_exists
from augutils.workspace_cleaner import clean_workspace_storage
from augutils.process_manager import ensure_vscode_closed, launch_vscode

def main():
    """主函数"""
    # 检查命令行参数
    debug_mode = '--debug' in sys.argv or '-d' in sys.argv
    skip_vscode_close = '--skip-close' in sys.argv
    # 注意：调试模式不再自动跳过VSCode关闭检查
    no_restart = '--no-restart' in sys.argv
    auto_restart = '--auto-restart' in sys.argv

    print("Free AugmentCode - VSCode数据清理工具")
    print("=" * 60)

    if debug_mode:
        print("🔧 调试模式已启用")
    if no_restart:
        print("⚠️  已禁用VSCode自动重启")
    if auto_restart:
        print("🔄 已启用VSCode自动重启")

    if skip_vscode_close:
        print("⚠️  跳过VSCode关闭检查（调试模式）")
    else:
        # 确保VSCode完全关闭
        if not ensure_vscode_closed():
            print("⚠️  警告: VSCode进程可能未完全关闭，继续执行可能会导致数据不一致")
            user_input = input("是否继续执行? (y/N): ").strip().lower()
            if user_input not in ['y', 'yes']:
                print("操作已取消")
                return 1

    # 显示系统路径信息
    print("System Paths:")
    print(f"Home Directory: {get_home_dir()}")
    print(f"App Data Directory: {get_app_data_dir()}")
    print(f"Storage Path: {get_storage_path()}")
    print(f"DB Path: {get_db_path()}")
    print(f"Machine ID Path: {get_machine_id_path()}")
    print(f"Workspace Storage Path: {get_workspace_storage_path()}")

    # 预检查数据库中是否存在AugmentCode相关数据
    print("\n检查数据库中的AugmentCode相关数据:")
    check_result = check_augment_data_exists()

    if check_result['error']:
        print(f"❌ 数据库检查失败: {check_result['error']}")
        print("程序将停止执行")
        return 1

    if not check_result['exists']:
        print("❌ 数据库中没有找到AugmentCode相关数据")
        print(f"检查的记录数量: {check_result['count']}")
        print("\n" + "=" * 60)
        print("⚠️  程序停止执行")
        print("原因: 数据库中没有找到包含 'augment' 的相关数据")
        print("\n可能的原因:")
        print("1. VSCode从未安装过AugmentCode插件")
        print("2. AugmentCode插件从未登录过账号")
        print("3. AugmentCode数据已经被清理过")
        print("4. 使用了不同的VSCode配置目录")
        print("\n建议:")
        print("- 确认VSCode中已安装AugmentCode插件")
        print("- 确认插件已经登录过账号并使用过")
        print("- 检查VSCode配置目录是否正确")
        print("=" * 60)
        return 2  # 返回特殊退出码表示没有找到数据

    print(f"✅ 找到 {check_result['count']} 条AugmentCode相关记录")
    print("示例记录:")
    for i, key in enumerate(check_result['sample_keys'], 1):
        print(f"  {i}. {key}")
    if check_result['count'] > len(check_result['sample_keys']):
        print(f"  ... 还有 {check_result['count'] - len(check_result['sample_keys'])} 条记录")

    print("\nModifying Telemetry IDs:")
    try:
        result = modify_telemetry_ids()
        print("\nBackup created at:")
        print(f"Storage backup path: {result['storage_backup_path']}")
        if result['machine_id_backup_path']:
            print(f"Machine ID backup path: {result['machine_id_backup_path']}")

        print("\nOld IDs:")
        print(f"Machine ID: {result['old_machine_id']}")
        print(f"Device ID: {result['old_device_id']}")

        print("\nNew IDs:")
        print(f"Machine ID: {result['new_machine_id']}")
        print(f"Device ID: {result['new_device_id']}")

        print("\nCleaning SQLite Database:")
        try:
            db_result = clean_augment_data()
            print(f"Database backup created at: {db_result['db_backup_path']}")
            print(f"Deleted {db_result['deleted_rows']} rows containing 'augment' in their keys")

        except ValueError as ve:
            # 没有找到相关数据，停止执行
            print(f"❌ {ve}")
            print("\n" + "=" * 60)
            print("⚠️  程序停止执行")
            print("原因: 数据库中没有找到AugmentCode相关数据")
            print("\n可能的原因:")
            print("1. VSCode从未安装过AugmentCode插件")
            print("2. AugmentCode数据已经被清理过")
            print("3. 使用了不同的VSCode配置目录")
            print("4. 数据库文件损坏或格式不正确")
            print("\n建议:")
            print("- 确认VSCode中已安装AugmentCode插件")
            print("- 确认插件已经登录过账号")
            print("- 检查VSCode配置目录是否正确")
            print("=" * 60)
            return 2  # 返回特殊退出码表示没有找到数据

        except Exception as db_error:
            print(f"❌ 数据库清理失败: {db_error}")
            print("程序将停止执行")
            return 1

        print("\nCleaning Workspace Storage:")
        ws_result = clean_workspace_storage()
        print(f"Workspace backup created at: {ws_result['backup_path']}")
        print(f"Deleted {ws_result['deleted_files_count']} files from workspace storage")

        print("\n" + "=" * 60)
        print("🎉 所有操作已完成!")

        # 处理VSCode重启逻辑
        if no_restart:
            print("已跳过VSCode重启（--no-restart参数）")
            print("您可以手动重新启动 VS Code 并使用新邮箱登录 AugmentCode 插件。")
        elif auto_restart:
            print("正在自动重启VSCode...")
            launch_result = launch_vscode()
            if launch_result['success']:
                print("✅ VSCode已自动重启")
            else:
                print(f"❌ VSCode自动重启失败: {launch_result['error']}")
                print("请手动重新启动 VS Code 并使用新邮箱登录 AugmentCode 插件。")
        else:
            # 默认自动重启VSCode
            print("正在自动重启VSCode...")
            launch_result = launch_vscode()
            if launch_result['success']:
                print("✅ VSCode已自动重启")
            else:
                print(f"❌ VSCode自动重启失败: {launch_result['error']}")
                print("请手动重新启动 VS Code 并使用新邮箱登录 AugmentCode 插件。")

        print("=" * 60)
        print("✅ 程序执行完成！")
        print("请按回车键退出程序...")

        return 0

    except FileNotFoundError as e:
        print(f"❌ 错误: {e}")
        print("请检查VSCode是否已安装并且配置文件存在。")
        print("请按回车键退出程序...")
        return 1
    except Exception as e:
        print(f"❌ 发生未知错误: {e}")
        import traceback
        print("详细错误信息:")
        traceback.print_exc()
        print("请按回车键退出程序...")
        return 1


if __name__ == "__main__":
    try:
        # 运行主函数并获取退出码
        exit_code = main()

    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
        exit_code = 1
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
        import traceback
        print("详细错误信息:")
        traceback.print_exc()
        exit_code = 1

    # 无论什么情况都等待用户按键
    try:
        input("\n按回车键退出...")
    except:
        # 如果input也失败了，至少等待一下
        import time
        time.sleep(2)

    sys.exit(exit_code)